import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";

// TODO: Replace this with your actual backend URL.
const BACKEND_URL = "https://your-backend-api.com";

function NewTicketDetailsForm({ token }) {
  const navigate = useNavigate();
  const accessToken = token || localStorage.getItem("access");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [hierarchicalData, setHierarchicalData] = useState(null);

  const [formData, setFormData] = useState({
    sensorType: "",
    familyName: "",
    modelNumber: "",
    serialNumber: "",
    sdkSoftwareUsed: "",
    sdkVersion: "",
    programmingLanguage: "",
    cameraConfigurationTool: "",
    operatingSystemDetailed: "",
    purchasedFrom: "",
    yearOfPurchase: "",
    poNumber: "",
    software: "",
  });

  // CSS Styles (Glassmorphism + Animations)
  const componentStyles = `
    @keyframes fadeInUp {
      to { opacity: 1; transform: translateY(0); }
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .new-ticket-container {
      min-height: 100vh;
      background: linear-gradient(to bottom right, #1E3A8A, #3B82F6);
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 2rem;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    }

    .glass-card {
      max-width: 800px;
      width: 100%;
      max-height: 90vh;
      overflow-y: auto;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(12px);
      border-radius: 1rem;
      border: 1px solid rgba(255, 255, 255, 0.2);
      padding: 2.5rem;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.37);
      animation: fadeInUp 0.6s ease-out forwards;
      opacity: 0;
      transform: translateY(20px);
      scrollbar-width: thin;
      scrollbar-color: #3B82F6 rgba(255, 255, 255, 0.1);
    }
    .glass-card::-webkit-scrollbar { width: 8px; }
    .glass-card::-webkit-scrollbar-thumb {
      background-color: #3B82F6;
      border-radius: 10px;
    }

    .main-heading {
      color: white;
      margin-bottom: 1.5rem;
      text-align: center;
      font-size: 2.5rem;
      font-weight: bold;
      text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .sub-heading {
      font-size: 1.1rem;
      color: rgba(255, 255, 255, 0.9);
      margin-bottom: 2rem;
      text-align: center;
    }

    .hierarchy-box {
      background-color: #DBEAFE;
      padding: 16px 20px;
      border-radius: 0.75rem;
      margin-bottom: 2rem;
      border: 2px solid #BFDBFE;
    }
    .hierarchy-box h3 { margin: 0 0 10px; color: #1E40AF; font-weight: 600; }
    .hierarchy-box p { margin: 0; font-size: 1rem; font-weight: 600; color: #1E3A8A; }

    .error-box {
      background-color: #FEE2E2;
      color: #DC2626;
      padding: 12px 16px;
      border-radius: 0.75rem;
      margin-bottom: 20px;
      border: 2px solid #FECACA;
      font-weight: 600;
      text-align: center;
    }

    .ticket-form { display: flex; flex-direction: column; gap: 20px; }

    .form-label {
      margin-bottom: 8px;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.9);
    }

    .glass-input {
      width: 100%;
      box-sizing: border-box;
      background: rgba(0, 0, 0, 0.25);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 0.75rem;
      padding: 14px 16px;
      color: white;
      font-size: 1rem;
      transition: all 0.2s ease-in-out;
    }
    .glass-input:focus {
      outline: none;
      border-color: #60A5FA;
      background: rgba(0, 0, 0, 0.4);
      box-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
    }
    .glass-input::placeholder { color: rgba(255, 255, 255, 0.6); }

    select.glass-input {
      appearance: none;
      background-image: url('data:image/svg+xml,%3Csvg%20xmlns%3D%22http://www.w3.org/2000/svg%22%20fill%3D%22white%22%20viewBox%3D%220%200%2024%2024%22%3E%3Cpath%20d%3D%22M7%2010l5%205%205-5z%22/%3E%3C/svg%3E');
      background-repeat: no-repeat;
      background-position: right 1rem center;
      background-size: 1em;
      padding-right: 2.5rem;
    }
    select.glass-input option {
      background: #1e293b;
      color: white;
    }

    .button-container {
      display: flex;
      gap: 16px;
      justify-content: center;
      margin-top: 2rem;
    }
    .btn {
      padding: 14px 24px;
      border: none;
      border-radius: 0.75rem;
      font-size: 1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease-in-out;
    }
    .btn-cancel { background-color: #6B7280; color: white; }
    .btn-cancel:hover { background-color: #4B5563; }
    .btn-submit {
      background: linear-gradient(to right, #3B82F6, #1E40AF);
      color: white;
      font-weight: bold;
      background-size: 200% 100%;
    }
    .btn-submit:hover { background-position: right center; }
    .btn-submit:disabled { background: #9CA3AF; cursor: not-allowed; }

    .spinner {
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-top: 4px solid white;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
      margin: 0 auto 20px;
    }
  `;

  // Load product data from sessionStorage
  useEffect(() => {
    const storedData = sessionStorage.getItem("hierarchicalSelection");
    if (!storedData) {
      navigate("/hierarchical-selection");
      return;
    }
    try {
      const data = JSON.parse(storedData);
      setHierarchicalData(data);
      setFormData((prev) => ({
        ...prev,
        sensorType:
          data.productSubcategory === "Area Scan" ||
          data.productSubcategory === "Line Scan"
            ? data.productSubcategory
            : "",
        familyName: data.productFamily || "",
        sdkSoftwareUsed:
          data.productCategory === "Software" ? data.productSubcategory : "",
      }));
    } catch (err) {
      console.error("Error parsing hierarchical data:", err);
      navigate("/hierarchical-selection");
    }
  }, [navigate]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    if (error) setError("");
  };

  const validateForm = () => {
    const requiredFields = [
      "modelNumber",
      "serialNumber",
      "purchasedFrom",
      "yearOfPurchase",
      "operatingSystemDetailed",
      "poNumber",
    ];
    for (let field of requiredFields) {
      if (!formData[field]?.trim()) {
        return `Please fill in the ${field.replace(/([A-Z])/g, " $1").toLowerCase()} field.`;
      }
    }
    return null;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    if (!hierarchicalData) {
      setError("Hierarchical selection data is missing. Please start over.");
      return;
    }

    setLoading(true);
    setError("");

    try {
      const selectedBrand = sessionStorage.getItem("selectedBrand") || "";

      const response = await fetch(`${BACKEND_URL}/api/create_ticket/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          product_type: hierarchicalData.productCategory,
          product_hierarchy_path: hierarchicalData.path,
          product_category: hierarchicalData.productCategory,
          product_subcategory: hierarchicalData.productSubcategory,
          product_family: hierarchicalData.productFamily,
          product_interface: hierarchicalData.productInterface,
          brand: selectedBrand,
          ...formData,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        sessionStorage.removeItem("hierarchicalSelection");
        sessionStorage.removeItem("selectedBrand");
        navigate(`/problem-categories?ticket=${data.ticket_number}`);
      } else {
        setError(data.message || "Failed to create ticket. Please try again.");
      }
    } catch (err) {
      console.error("Error creating ticket:", err);
      setError("Network error. Please check your connection and try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    sessionStorage.removeItem("hierarchicalSelection");
    sessionStorage.removeItem("selectedBrand");
    navigate("/select-brand");
  };

  if (!hierarchicalData) {
    return (
      <div className="new-ticket-container">
        <div>
          <div className="spinner"></div>
          <h2 style={{ color: "white", textAlign: "center" }}>
            Loading Product Details...
          </h2>
        </div>
        <style>{componentStyles}</style>
      </div>
    );
  }

  return (
    <>
      <style>{componentStyles}</style>
      <div className="new-ticket-container">
        <div className="glass-card">
          <h1 className="main-heading">Product Details</h1>

          <div className="hierarchy-box">
            <h3>Selected Product:</h3>
            <p>{hierarchicalData.path.join(" > ")}</p>
          </div>

          <p className="sub-heading">
            Please provide additional details about your product. Fields marked with * are required.
          </p>

          {error && <div className="error-box">{error}</div>}

          <form onSubmit={handleSubmit} className="ticket-form">
            {/* Model Number */}
            <div>
              <label className="form-label">Model Number *</label>
              <input
                type="text"
                name="modelNumber"
                value={formData.modelNumber}
                onChange={handleInputChange}
                className="glass-input"
                placeholder="Enter exact model number"
                required
              />
            </div>

            {/* Serial Number */}
            <div>
              <label className="form-label">Serial Number *</label>
              <input
                type="text"
                name="serialNumber"
                value={formData.serialNumber}
                onChange={handleInputChange}
                className="glass-input"
                placeholder="Enter serial number"
                required
              />
            </div>

            {/* SDK Version - only show for software */}
            {hierarchicalData.productCategory === "Software" && (
              <div>
                <label className="form-label">SDK Version</label>
                <input
                  type="text"
                  name="sdkVersion"
                  value={formData.sdkVersion}
                  onChange={handleInputChange}
                  className="glass-input"
                  placeholder="e.g., 8.90, 3.1"
                />
              </div>
            )}

            {/* Software */}
            <div>
              <label className="form-label">Software</label>
              <select
                name="software"
                value={formData.software}
                onChange={handleInputChange}
                className="glass-input"
              >
                <option value="">Select Software</option>
                <option value="Sapera LT (v9.0)">Sapera LT (v9.0)</option>
                <option value="Spinnaker (v4.2)">Spinnaker (v4.2)</option>
              </select>
            </div>

            {/* Programming Language */}
            <div>
              <label className="form-label">Programming Language</label>
              <select
                name="programmingLanguage"
                value={formData.programmingLanguage}
                onChange={handleInputChange}
                className="glass-input"
              >
                <option value="">Select Language</option>
                <option value="C++">C++</option>
                <option value="C#">C#</option>
                <option value="Python">Python</option>
                <option value="Java">Java</option>
                <option value="LabVIEW">LabVIEW</option>
                <option value="Other">Other</option>
              </select>
            </div>

            {/* Operating System */}
            <div>
              <label className="form-label">Operating System *</label>
              <select
                name="operatingSystemDetailed"
                value={formData.operatingSystemDetailed}
                onChange={handleInputChange}
                className="glass-input"
                required
              >
                <option value="">Select Operating System</option>
                <option value="Windows 10">Windows 10</option>
                <option value="Windows 11">Windows 11</option>
                <option value="Ubuntu 22.04">Ubuntu 22.04</option>
                <option value="macOS">macOS</option>
                <option value="Other">Other</option>
              </select>
            </div>

            {/* Purchased From */}
            <div>
              <label className="form-label">Purchased From *</label>
              <input
                type="text"
                name="purchasedFrom"
                value={formData.purchasedFrom}
                onChange={handleInputChange}
                className="glass-input"
                placeholder="Dealer/Distributor name"
                required
              />
            </div>

            {/* Year of Purchase */}
            <div>
              <label className="form-label">Year of Purchase *</label>
              <select
                name="yearOfPurchase"
                value={formData.yearOfPurchase}
                onChange={handleInputChange}
                className="glass-input"
                required
              >
                <option value="">Select Year</option>
                {Array.from({ length: 10 }, (_, i) => new Date().getFullYear() - i).map(
                  (year) => (
                    <option key={year} value={year}>
                      {year}
                    </option>
                  )
                )}
              </select>
            </div>

            {/* PO Number */}
            <div>
              <label className="form-label">PO Number *</label>
              <input
                type="text"
                name="poNumber"
                value={formData.poNumber}
                onChange={handleInputChange}
                className="glass-input"
                placeholder="Purchase Order Number"
                required
              />
            </div>

            <div className="button-container">
              <button
                type="button"
                onClick={handleCancel}
                className="btn btn-cancel"
              >
                Back
              </button>
              <button
                type="submit"
                disabled={loading}
                className="btn btn-submit"
              >
                {loading ? "Creating Ticket..." : "Continue"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </>
  );
}

export default NewTicketDetailsForm;
