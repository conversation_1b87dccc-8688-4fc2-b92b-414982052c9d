body {
  margin: 0;
  background: linear-gradient(to bottom right, #1E3A8A, #3B82F6);
  font-family: Arial, sans-serif;
  display: flex;
  justify-content: center;
  align-items: start;
  min-height: 100vh;
  padding: 40px 20px;
}

.prompt-container {
  width: 100%;
  max-width: 900px;
  background: rgba(255, 255, 255, 0.95);
  padding: 2rem;
  border-radius: 1.5rem;
  box-shadow: 0 20px 25px rgba(0, 0, 0, 0.25);
  backdrop-filter: blur(10px);
}

.prompt-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.prompt-header h1 {
  font-size: 2rem;
  font-weight: 600;
  color: #1E3A8A;
  display: flex;
  align-items: center;
  gap: 8px;
}

.top-buttons {
  display: flex;
  gap: 12px;
}

.nav-link,
.logout-button {
  background-color: #2563EB;
  border: none;
  color: white;
  font-weight: 600;
  padding: 10px 16px;
  border-radius: 0.75rem;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.nav-link:hover,
.logout-button:hover {
  background-color: #1E40AF;
  transform: translateY(-1px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.prompt-label {
  font-weight: bold;
  margin-top: 20px;
  display: block;
}

.prompt-select,
.prompt-textarea {
  width: 100%;
  padding: 14px 16px;
  margin-top: 6px;
  border: 2px solid #E5E7EB;
  border-radius: 0.75rem;
  margin-bottom: 16px;
  font-size: 1rem;
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  background-color: #ffffff;
  box-sizing: border-box;
}

.prompt-select:focus,
.prompt-textarea:focus {
  outline: none;
  border-color: #3B82F6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.prompt-textarea {
  height: 300px;
  resize: none;
  font-family: monospace;
  font-size: 14px;
}

.prompt-buttons {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.save-btn {
  background-color: #2563EB;
  color: white;
  font-weight: 600;
  border: none;
  padding: 12px 24px;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.save-btn:hover {
  background-color: #1E40AF;
  transform: translateY(-1px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.prompt-message {
  margin-top: 20px;
  font-size: 14px;
}
