import React, { useState, useEffect, useRef } from "react";
import { apiGet, apiPost, BACKEND_URL } from "./utils/api";
import "./App.css";

export default function AdminChatbot({ token }) {
  const accessToken = token || localStorage.getItem("access");

  const [query, setQuery] = useState("");
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: "bot",
      content: "👋 Welcome, Admin! Ask me anything.",
      timestamp: new Date(),
    },
  ]);
  const [loading, setLoading] = useState(false);
  const [promptTemplate, setPromptTemplate] = useState(null);
  const [pendingFiles, setPendingFiles] = useState(null);
  const [awaitingFileChoice, setAwaitingFileChoice] = useState(false);

  const endRef = useRef(null);
  useEffect(() => endRef.current?.scrollIntoView({ behavior: "smooth" }), [messages]);

  useEffect(() => {
    fetch(`${BACKEND_URL}/api/prompts/?type=chat`)
      .then((r) => r.json())
      .then((d) => setPromptTemplate(d.template))
      .catch(() => {});
  }, []);

  const formatTime = (ts) => ts.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });

  // Render messages with clickable links
  const renderMessages = () =>
    messages.map((m) => (
      <div
        key={m.id}
        style={{ textAlign: m.type === "user" ? "right" : "left", marginBottom: 8 }}
        aria-live="polite"
      >
        <div
          style={{
            display: "inline-block",
            maxWidth: "75%",
            padding: "10px 14px",
            borderRadius: 12,
            backgroundColor: m.type === "user" ? "#dcf8c6" : "#e3e3e3",
            whiteSpace: "pre-wrap",
            color: "#333",
            fontSize: "1rem",
          }}
        >
          {renderWithLinks(m.content)}
          <div style={{ fontSize: "0.7em", color: "#555", marginTop: 6 }}>{formatTime(m.timestamp)}</div>
        </div>
      </div>
    ));

  // Convert markdown-style links [text](url) to anchor tags with token if file links
  const renderWithLinks = (text) => {
    const token = accessToken;
    const lines = text.split("\n");

    return lines.map((line, idx) => {
      const regex = /\[([^\]]+)\]\(([^)]+)\)/g;
      const parts = [];
      let lastIndex = 0;
      let match;
      let keyIndex = 0;

      while ((match = regex.exec(line)) !== null) {
        if (match.index > lastIndex) {
          parts.push(<span key={`${idx}-text-${keyIndex++}`}>{line.substring(lastIndex, match.index)}</span>);
        }

        let href = match[2];

        // Append token if link is to backend files endpoint and token not present
        if (
          (href.startsWith(`${BACKEND_URL}/api/files/`) || href.startsWith("/api/files/")) &&
          token &&
          !href.includes("token=")
        ) {
          href += href.includes("?") ? `&token=${token}` : `?token=${token}`;
        }

        parts.push(
          <a
            key={`${idx}-link-${keyIndex++}`}
            href={href.startsWith("http") ? href : `${BACKEND_URL}${href}`} // prepend backend if relative URL
            target="_blank"
            rel="noopener noreferrer"
            style={{ color: "#007bff" }}
          >
            {match[1]}
          </a>
        );

        lastIndex = regex.lastIndex;
      }

      if (lastIndex < line.length) {
        parts.push(<span key={`${idx}-text-last`}>{line.substring(lastIndex)}</span>);
      }

      return (
        <div key={idx} style={{ marginBottom: 2 }}>
          {parts.length > 0 ? parts : line}
        </div>
      );
    });
  };

  // Send user input to backend
  const sendToBackend = async (userText) => {
    const historyText = messages
      .filter((m) => m.type === "user" || m.type === "bot")
      .map((m) => `${m.type === "user" ? "User" : "Bot"}: ${m.content}`)
      .join("\n");

    const finalPrompt = promptTemplate
      ? promptTemplate
          .replace("{context_text}", "admin context")
          .replace("{history_text}", historyText)
          .replace("{query}", userText)
      : userText;

    const res = await apiPost(`${BACKEND_URL}/api/chat/`, {
      query: finalPrompt,
      ticket_mode: false,
    });

    const data = await res.json();
    if (!res.ok) throw new Error(data.error || "Server error");

    setMessages((prev) => [
      ...prev,
      { id: Date.now(), type: "bot", content: data.answer || "No answer.", timestamp: new Date() },
    ]);

    // If backend returned related files, prompt user once
    const files = (data.files || []).filter((f) => f.filename);
    if (files.length) {
      setPendingFiles(files);
      setAwaitingFileChoice(true);
      setMessages((prev) => [
        ...prev,
        {
          id: Date.now() + 1,
          type: "bot",
          content: "💡 For full explanation, do you want the related file? (yes/no)",
          timestamp: new Date(),
        },
      ]);
    }
  };

  // Handle user submitting input
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!query.trim() || loading) return;

    const userInput = query.trim();
    setQuery("");
    setMessages((prev) => [
      ...prev,
      { id: Date.now(), type: "user", content: userInput, timestamp: new Date() },
    ]);
    setLoading(true);

    // If waiting for yes/no about related files
    if (awaitingFileChoice) {
      const ans = userInput.toLowerCase();
      if (ans === "yes" && pendingFiles) {
        // Show related files as clickable links with full backend URLs
        const links = pendingFiles
          .map(
            (f, i) =>
              `${i + 1}. [${f.filename}](${f.url || `${BACKEND_URL}/api/files/${encodeURIComponent(f.filename)}`})`
          )
          .join("\n");

        setMessages((prev) => [
          ...prev,
          {
            id: Date.now() + 2,
            type: "bot",
            content: `📎 Related files:\n${links}`,
            timestamp: new Date(),
          },
        ]);
      } else if (ans === "no") {
        setMessages((prev) => [
          ...prev,
          {
            id: Date.now() + 2,
            type: "bot",
            content: "👍 Okay, no files will be sent.",
            timestamp: new Date(),
          },
        ]);
      } else {
        setMessages((prev) => [
          ...prev,
          {
            id: Date.now() + 2,
            type: "bot",
            content: "Please answer 'yes' or 'no'. Do you want the related file?",
            timestamp: new Date(),
          },
        ]);
        setLoading(false);
        return;
      }

      setPendingFiles(null);
      setAwaitingFileChoice(false);
      setLoading(false);
      return;
    }

    // Normal chat flow
    try {
      await sendToBackend(userInput);
    } catch (err) {
      setMessages((prev) => [
        ...prev,
        {
          id: Date.now(),
          type: "bot",
          content: `❌ ${err.message}`,
          timestamp: new Date(),
        },
      ]);
    }
    setLoading(false);
  };

  return (
    <div className="blue-gradient-bg" style={{ minHeight: "100vh", padding: "2rem" }}>
      <div className="blue-container">
        <div className="chat-container">
          <header className="chat-header">
            <h1 className="blue-heading" style={{ margin: 0, fontSize: "2rem" }}>ADMIN CHATBOT</h1>
            <p className="blue-text-light" style={{ margin: "0.5rem 0 0 0", opacity: 0.9 }}>Technical Support Assistant</p>
          </header>

          <div className="chat-messages" aria-live="polite" aria-relevant="additions" style={{
            flex: 1,
            overflowY: "auto",
            padding: "20px",
            background: "rgba(248, 250, 252, 0.95)",
            display: "flex",
            flexDirection: "column",
            gap: "15px"
          }}>
            {renderMessages()}
            {loading && (
              <div className="typing" aria-label="AI is thinking" style={{
                display: "flex",
                alignItems: "center",
                gap: "8px",
                padding: "12px",
                color: "#3B82F6",
                fontWeight: "500"
              }}>
                <span className="dot" style={{ backgroundColor: "#2563EB" }}></span>
                <span className="dot" style={{ backgroundColor: "#3B82F6" }}></span>
                <span className="dot" style={{ backgroundColor: "#60A5FA" }}></span>
                Analyzing…
              </div>
            )}
            <div ref={endRef} />
          </div>

          <div className="chat-input" style={{
            padding: "20px",
            background: "white",
            borderTop: "1px solid #E5E7EB"
          }}>
            <form onSubmit={handleSubmit} style={{ display: "flex", gap: "12px" }} aria-label="Send message form">
              <textarea
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="Type your question here…"
                disabled={loading}
                rows={2}
                aria-label="Chat input"
                className="blue-input"
                style={{
                  flex: 1,
                  resize: "none",
                  minHeight: "50px",
                  fontSize: "16px",
                  border: "2px solid #E5E7EB",
                  borderRadius: "0.75rem",
                  padding: "12px 16px"
                }}
                onKeyDown={(e) => {
                  if (e.key === "Enter" && !e.shiftKey) {
                    e.preventDefault();
                    handleSubmit(e);
                  }
                }}
              />
              <button
                type="submit"
                disabled={loading || !query.trim()}
                aria-label="Send message"
                className="blue-button"
                style={{
                  padding: "12px 24px",
                  background: loading ? "#9CA3AF" : "#2563EB",
                  color: "white",
                  border: "none",
                  borderRadius: "0.75rem",
                  cursor: loading ? "not-allowed" : "pointer",
                  fontSize: "16px",
                  fontWeight: "600",
                  transition: "all 0.2s ease-in-out"
                }}
              >
                {loading ? "…" : "Send"}
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
